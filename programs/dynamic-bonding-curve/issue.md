 [H-02] Metadata PDA not verified (PDA substitution risk)

Pattern match: PDA substitution check (primer)
Where: mint_metadata: UncheckedAccount<'info> is only mut. The code delegates to process_create_token_metadata(...); in this scope we don’t see a guarantee that mint_metadata.key() equals the canonical Metaplex Metadata PDA for base_mint.

Impact (Usurp/Lock): If the helper doesn’t assert the PDA, a caller can pass an arbitrary account; depending on the CPI path, this can brick metadata, pay fees to attacker accounts, or create mismatched metadata.

Fix: Derive and enforce the PDA here before calling the helper.

use mpl_token_metadata::pda::find_metadata_account;

let (expected_md, _) = find_metadata_account(&ctx.accounts.base_mint.key());
require_keys_eq!(ctx.accounts.mint_metadata.key(), expected_md, PoolError::InvalidMetadataPda);


(or express it as #[account(address = expected_md)] via precomputed seeds if you move derivation to the accounts struct)